<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AureaVoice - Audio Recording App</title>
  </head>
  <body>
    <div id="app">
      <header>
        <h1>AureaVoice</h1>
        <p>Record your voice with high quality audio processing</p>
      </header>

      <main>
        <section id="text-prompt-section">
          <h2>Read this text:</h2>
          <p id="text-prompt" class="prompt-text">Loading text prompt...</p>
        </section>

        <section id="recording-controls">
          <div class="control-group">
            <button id="record-btn" class="btn btn-primary">🎤 Start Recording</button>
          </div>

          <div class="control-group">
            <label for="file-input" class="btn btn-outline">📁 Upload Audio File</label>
            <input type="file" id="file-input" accept="audio/*" style="display: none;">
          </div>
        </section>

        <section id="recording-status">
          <div id="recording-indicator" class="hidden">
            <span class="recording-dot"></span>
            <span>Recording...</span>
          </div>
          <div id="processing-indicator" class="hidden">
            <span class="spinner"></span>
            <span>Processing audio...</span>
          </div>
        </section>

        <section id="waveform-section" class="hidden">
          <h3>Live Waveform</h3>
          <div id="waveform-container"></div>
        </section>

        <section id="audio-playback">
          <h3>Your Recordings</h3>
          <div id="recordings-list" class="recordings-container">
            <p class="no-recordings">No recordings yet. Start recording to see your audio files here.</p>
          </div>
        </section>
      </main>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
