import { BaseView } from './BaseView.js';

/**
 * Audio Recording View
 * Handles UI for audio recording functionality
 */
export class AudioRecordingView extends BaseView {
  constructor(element) {
    super(element);
    this.recordButton = null;
    this.fileInput = null;
    this.textPrompt = null;
    this.recordingIndicator = null;
    this.processingIndicator = null;
    this.recordingsList = null;
    this.waveformSection = null;
    this.waveformContainer = null;
    this.wavesurfer = null;
    this.recordPlugin = null;
    this.initializeElements();
  }

  /**
   * Initialize DOM elements
   */
  initializeElements() {
    this.recordButton = this.querySelector('#record-btn');
    this.fileInput = this.querySelector('#file-input');
    this.textPrompt = this.querySelector('#text-prompt');
    this.recordingIndicator = this.querySelector('#recording-indicator');
    this.processingIndicator = this.querySelector('#processing-indicator');
    this.recordingsList = this.querySelector('#recordings-list');
    this.waveformSection = this.querySelector('#waveform-section');
    this.waveformContainer = this.querySelector('#waveform-container');
  }

  /**
   * Set up event handlers
   * @param {Object} handlers - Event handler functions
   */
  setupEventHandlers(handlers) {
    if (this.recordButton && handlers.onRecordToggle) {
      this.addEventListener(this.recordButton, 'click', handlers.onRecordToggle);
    }

    if (this.fileInput && handlers.onFileSelect) {
      this.addEventListener(this.fileInput, 'change', handlers.onFileSelect);
    }

    if (this.textPrompt && handlers.onPromptClick) {
      this.addEventListener(this.textPrompt, 'click', handlers.onPromptClick);
    }
  }

  /**
   * Update text prompt
   * @param {string} text - Prompt text
   */
  updateTextPrompt(text) {
    this.setText(this.textPrompt, text);
  }

  /**
   * Show recording started state
   */
  async showRecordingStarted() {
    this.setEnabled(this.recordButton, true);
    this.setEnabled(this.fileInput, false);
    this.show(this.recordingIndicator);
    this.hide(this.processingIndicator);

    this.setText(this.recordButton, '⏹️ Stop Recording');
    this.recordButton.classList.add('recording');

    // Start waveform visualization
    await this.startRecordingWaveform();
  }

  /**
   * Show recording stopped state
   */
  showRecordingStopped() {
    this.setEnabled(this.recordButton, true);
    this.setEnabled(this.fileInput, true);
    this.hide(this.recordingIndicator);

    this.setText(this.recordButton, '🎤 Start Recording');
    this.recordButton.classList.remove('recording');

    // Stop waveform visualization
    this.stopRecordingWaveform();
  }

  /**
   * Show processing state
   */
  showProcessing() {
    this.setEnabled(this.recordButton, false);
    this.setEnabled(this.fileInput, false);
    this.hide(this.recordingIndicator);
    this.show(this.processingIndicator);
  }

  /**
   * Hide processing state
   */
  hideProcessing() {
    this.hide(this.processingIndicator);
    this.setEnabled(this.recordButton, true);
    this.setEnabled(this.fileInput, true);
  }

  /**
   * Show error message
   * @param {string} message - Error message
   */
  showError(message) {
    // Create or update error display
    let errorElement = this.querySelector('.error-message');
    if (!errorElement) {
      errorElement = document.createElement('div');
      errorElement.className = 'error-message';
      this.querySelector('#recording-controls').appendChild(errorElement);
    }
    
    errorElement.textContent = message;
    errorElement.style.display = 'block';
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
      if (errorElement) {
        errorElement.style.display = 'none';
      }
    }, 5000);
  }

  /**
   * Update recordings list
   * @param {Array} recordings - Array of recording objects
   */
  updateRecordingsList(recordings) {
    if (!this.recordingsList) return;

    if (recordings.length === 0) {
      this.setHTML(this.recordingsList, 
        '<p class="no-recordings">No recordings yet. Start recording to see your audio files here.</p>'
      );
      return;
    }

    const recordingsHTML = recordings.map(recording => this.createRecordingHTML(recording)).join('');
    this.setHTML(this.recordingsList, recordingsHTML);

    // Add event listeners for recording controls
    this.setupRecordingControls();
  }

  /**
   * Create HTML for a single recording
   * @param {Object} recording - Recording object
   * @returns {string} HTML string
   */
  createRecordingHTML(recording) {
    const date = new Date(recording.timestamp).toLocaleString();
    const duration = recording.duration ? `${recording.duration.toFixed(1)}s` : 'Unknown';
    const size = this.formatFileSize(recording.size);
    const type = recording.isUpload ? 'Uploaded' : 'Recorded';

    return `
      <div class="recording-item" data-recording-id="${recording.id}">
        <div class="recording-info">
          <div class="recording-title">
            <span class="recording-name">${recording.filename}</span>
            <span class="recording-type">${type}</span>
          </div>
          <div class="recording-details">
            <span class="recording-date">${date}</span>
            <span class="recording-duration">${duration}</span>
            <span class="recording-size">${size}</span>
          </div>
        </div>
        <div class="recording-controls">
          <button class="btn btn-small play-btn" data-action="play" data-recording-id="${recording.id}">
            ▶️ Play
          </button>
          <button class="btn btn-small download-btn" data-action="download" data-recording-id="${recording.id}">
            💾 Download
          </button>
          <button class="btn btn-small btn-danger delete-btn" data-action="delete" data-recording-id="${recording.id}">
            🗑️ Delete
          </button>
        </div>
      </div>
    `;
  }

  /**
   * Setup event handlers for recording controls
   */
  setupRecordingControls() {
    // Play buttons
    this.querySelectorAll('.play-btn').forEach(btn => {
      this.addEventListener(btn, 'click', (e) => {
        const recordingId = e.target.dataset.recordingId;
        this.element.dispatchEvent(new CustomEvent('playRecording', { 
          detail: { recordingId } 
        }));
      });
    });

    // Download buttons
    this.querySelectorAll('.download-btn').forEach(btn => {
      this.addEventListener(btn, 'click', (e) => {
        const recordingId = e.target.dataset.recordingId;
        this.element.dispatchEvent(new CustomEvent('downloadRecording', { 
          detail: { recordingId } 
        }));
      });
    });

    // Delete buttons
    this.querySelectorAll('.delete-btn').forEach(btn => {
      this.addEventListener(btn, 'click', (e) => {
        const recordingId = e.target.dataset.recordingId;
        if (confirm('Are you sure you want to delete this recording?')) {
          this.element.dispatchEvent(new CustomEvent('deleteRecording', { 
            detail: { recordingId } 
          }));
        }
      });
    });
  }

  /**
   * Update play button state
   * @param {string} recordingId - Recording ID
   * @param {boolean} isPlaying - Whether audio is playing
   */
  updatePlayButton(recordingId, isPlaying) {
    const playBtn = this.querySelector(`[data-recording-id="${recordingId}"][data-action="play"]`);
    if (playBtn) {
      playBtn.textContent = isPlaying ? '⏸️ Pause' : '▶️ Play';
    }
  }

  /**
   * Format file size for display
   * @param {number} bytes - Size in bytes
   * @returns {string} Formatted size string
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Show success message
   * @param {string} message - Success message
   */
  showSuccess(message) {
    // Create or update success display
    let successElement = this.querySelector('.success-message');
    if (!successElement) {
      successElement = document.createElement('div');
      successElement.className = 'success-message';
      this.querySelector('#recording-controls').appendChild(successElement);
    }
    
    successElement.textContent = message;
    successElement.style.display = 'block';
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
      if (successElement) {
        successElement.style.display = 'none';
      }
    }, 3000);
  }

  /**
   * Clear file input
   */
  clearFileInput() {
    if (this.fileInput) {
      this.fileInput.value = '';
    }
  }

  /**
   * Initialize WaveSurfer for live recording waveform
   */
  async initializeWaveform() {
    try {
      // Import WaveSurfer and Record plugin dynamically
      const WaveSurfer = (await import('wavesurfer.js')).default;
      const RecordPlugin = (await import('wavesurfer.js/plugins/record')).default;

      // Clear any existing waveform
      if (this.wavesurfer) {
        this.wavesurfer.destroy();
      }

      // Create WaveSurfer instance
      this.wavesurfer = WaveSurfer.create({
        container: this.waveformContainer,
        waveColor: '#4F4A85',
        progressColor: '#383351',
        height: 80,
        normalize: true,
        fillParent: true,
        responsive: true
      });

      // Create Record plugin
      this.recordPlugin = this.wavesurfer.registerPlugin(RecordPlugin.create({
        continuousWaveform: true,
        continuousWaveformDuration: 30, // 30 seconds of waveform
        renderRecordedAudio: false,
        mimeType: 'audio/webm;codecs=opus',
        audioBitsPerSecond: 128000
      }));

      // Listen for recording events
      this.recordPlugin.on('record-end', (blob) => {
        this.handleRecordingComplete(blob);
        // Immediately cleanup microphone after recording ends
        setTimeout(() => {
          this.cleanupMicrophoneStream();
        }, 100);
      });

      return true;
    } catch (error) {
      console.error('Error initializing waveform:', error);
      return false;
    }
  }

  /**
   * Start recording with waveform visualization
   */
  async startRecordingWaveform() {
    try {
      if (!this.recordPlugin) {
        const initialized = await this.initializeWaveform();
        if (!initialized) {
          throw new Error('Failed to initialize waveform');
        }
      }

      // Show waveform section
      this.show(this.waveformSection);

      // Start recording with the plugin
      await this.recordPlugin.startRecording();

      return true;
    } catch (error) {
      console.error('Error starting recording waveform:', error);
      this.showError('Failed to start recording visualization.');
      return false;
    }
  }

  /**
   * Stop recording waveform
   */
  stopRecordingWaveform() {
    try {
      if (this.recordPlugin && this.recordPlugin.isRecording()) {
        this.recordPlugin.stopRecording();
      }

      // Properly cleanup microphone stream
      this.cleanupMicrophoneStream();

      // Destroy and recreate the plugin to ensure complete cleanup
      setTimeout(() => {
        this.cleanupWaveform();
        this.hide(this.waveformSection);
      }, 500);
    } catch (error) {
      console.error('Error stopping recording waveform:', error);
    }
  }

  /**
   * Cleanup microphone stream to release microphone access
   */
  cleanupMicrophoneStream() {
    try {
      if (this.recordPlugin) {
        // Use the plugin's built-in stopMic method to properly release microphone
        this.recordPlugin.stopMic();
      }
    } catch (error) {
      console.error('Error cleaning up microphone stream:', error);
    }
  }

  /**
   * Handle recording completion from WaveSurfer Record plugin
   */
  handleRecordingComplete(blob) {
    // Dispatch custom event with the recorded blob
    this.element.dispatchEvent(new CustomEvent('recordingComplete', {
      detail: { audioBlob: blob }
    }));
  }

  /**
   * Get the WaveSurfer Record plugin instance
   */
  getRecordPlugin() {
    return this.recordPlugin;
  }

  /**
   * Cleanup waveform resources
   */
  cleanupWaveform() {
    // First cleanup microphone stream and destroy record plugin
    if (this.recordPlugin) {
      this.recordPlugin.stopMic();
      this.recordPlugin.destroy();
      this.recordPlugin = null;
    }

    if (this.wavesurfer) {
      this.wavesurfer.destroy();
      this.wavesurfer = null;
    }
  }
}
