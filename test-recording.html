<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Recording Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>AureaVoice Recording Functionality Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Basic Application Loading</h2>
        <button onclick="testAppLoading()">Test App Loading</button>
        <div id="app-loading-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Recording Button Toggle</h2>
        <button onclick="testRecordingToggle()">Test Recording Toggle</button>
        <div id="recording-toggle-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: WaveSurfer Import</h2>
        <button onclick="testWaveSurferImport()">Test WaveSurfer Import</button>
        <div id="wavesurfer-import-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 4: Microphone Access</h2>
        <button onclick="testMicrophoneAccess()">Test Microphone Access</button>
        <div id="microphone-access-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 5: Recordings List Sorting</h2>
        <button onclick="testRecordingsListSorting()">Test Recordings List Sorting</button>
        <div id="recordings-list-result"></div>
    </div>

    <script type="module">
        // Test functions
        window.testAppLoading = async function() {
            const resultDiv = document.getElementById('app-loading-result');
            try {
                // Check if the main app is accessible
                if (window.aureaVoiceApp) {
                    resultDiv.innerHTML = '<div class="test-result success">✅ App is loaded and accessible</div>';
                } else {
                    resultDiv.innerHTML = '<div class="test-result error">❌ App is not loaded or not accessible</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">❌ Error testing app loading: ${error.message}</div>`;
            }
        };

        window.testRecordingToggle = async function() {
            const resultDiv = document.getElementById('recording-toggle-result');
            try {
                // Check if record button exists and has correct initial state
                const recordButton = document.getElementById('record-btn');
                if (recordButton) {
                    const initialText = recordButton.textContent;
                    if (initialText.includes('Start Recording')) {
                        resultDiv.innerHTML = '<div class="test-result success">✅ Record button found with correct initial state</div>';
                    } else {
                        resultDiv.innerHTML = `<div class="test-result error">❌ Record button has unexpected text: ${initialText}</div>`;
                    }
                } else {
                    resultDiv.innerHTML = '<div class="test-result error">❌ Record button not found</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">❌ Error testing recording toggle: ${error.message}</div>`;
            }
        };

        window.testWaveSurferImport = async function() {
            const resultDiv = document.getElementById('wavesurfer-import-result');
            try {
                // Test WaveSurfer import
                const WaveSurfer = (await import('wavesurfer.js')).default;
                const RecordPlugin = (await import('wavesurfer.js/plugins/record')).default;
                
                if (WaveSurfer && RecordPlugin) {
                    resultDiv.innerHTML = '<div class="test-result success">✅ WaveSurfer and Record plugin imported successfully</div>';
                } else {
                    resultDiv.innerHTML = '<div class="test-result error">❌ Failed to import WaveSurfer or Record plugin</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">❌ Error importing WaveSurfer: ${error.message}</div>`;
            }
        };

        window.testMicrophoneAccess = async function() {
            const resultDiv = document.getElementById('microphone-access-result');
            try {
                // Test microphone access
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                if (stream) {
                    // Stop the stream immediately
                    stream.getTracks().forEach(track => track.stop());
                    resultDiv.innerHTML = '<div class="test-result success">✅ Microphone access granted</div>';
                } else {
                    resultDiv.innerHTML = '<div class="test-result error">❌ Failed to get microphone access</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">❌ Microphone access denied or error: ${error.message}</div>`;
            }
        };

        window.testRecordingsListSorting = async function() {
            const resultDiv = document.getElementById('recordings-list-result');
            try {
                // Test recordings list sorting logic
                const testRecordings = [
                    { id: '1', timestamp: 1000, filename: 'old.wav' },
                    { id: '2', timestamp: 3000, filename: 'newest.wav' },
                    { id: '3', timestamp: 2000, filename: 'middle.wav' }
                ];
                
                // Sort by timestamp (newest first)
                const sorted = [...testRecordings].sort((a, b) => b.timestamp - a.timestamp);
                
                if (sorted[0].filename === 'newest.wav' && sorted[2].filename === 'old.wav') {
                    resultDiv.innerHTML = '<div class="test-result success">✅ Recordings list sorting works correctly (newest first)</div>';
                } else {
                    resultDiv.innerHTML = '<div class="test-result error">❌ Recordings list sorting is incorrect</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">❌ Error testing recordings list sorting: ${error.message}</div>`;
            }
        };

        // Auto-run some tests when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                testAppLoading();
                testRecordingToggle();
                testRecordingsListSorting();
            }, 1000);
        });
    </script>
</body>
</html>
