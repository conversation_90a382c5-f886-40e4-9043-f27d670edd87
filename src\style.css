:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Custom colors */
  --primary-color: #646cff;
  --primary-hover: #535bf2;
  --success-color: #4ade80;
  --error-color: #ef4444;
  --warning-color: #f59e0b;
  --background-color: #242424;
  --surface-color: #1a1a1a;
  --text-color: rgba(255, 255, 255, 0.87);
  --text-muted: #888;
  --border-color: #333;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--background-color);
  color: var(--text-color);
}

#app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* Header */
header {
  text-align: center;
  margin-bottom: 3rem;
}

header h1 {
  font-size: 3.2em;
  line-height: 1.1;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

header p {
  color: var(--text-muted);
  font-size: 1.1em;
  margin: 0;
}

/* Main sections */
main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

section {
  background: var(--surface-color);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
}

section h2, section h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--text-color);
}

/* Text prompt section */
#text-prompt-section {
  text-align: center;
}

.prompt-text {
  font-size: 1.2em;
  line-height: 1.6;
  padding: 1rem;
  background: rgba(100, 108, 255, 0.1);
  border-radius: 8px;
  border-left: 4px solid var(--primary-color);
  margin: 1rem 0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.prompt-text:hover {
  background: rgba(100, 108, 255, 0.15);
}

/* Recording controls */
#recording-controls {
  text-align: center;
}

.control-group {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.control-group:last-child {
  margin-bottom: 0;
}

/* Buttons */
.btn {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.75em 1.5em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.25s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.btn-primary.recording {
  background-color: var(--error-color);
  animation: pulse 2s infinite;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-secondary {
  background-color: var(--surface-color);
  color: var(--text-color);
  border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  border-color: var(--primary-color);
  background-color: rgba(100, 108, 255, 0.1);
}

.btn-outline {
  background-color: transparent;
  color: var(--text-color);
  border-color: var(--border-color);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--surface-color);
  border-color: var(--primary-color);
}
.btn-small {
  padding: 0.5em 1em;
  font-size: 0.9em;
}

.btn-danger {
  background-color: var(--error-color);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Recording status indicators */
#recording-status {
  text-align: center;
}

.recording-indicator, .processing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 8px;
  font-weight: 500;
}

.recording-indicator {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.processing-indicator {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.recording-dot {
  width: 8px;
  height: 8px;
  background-color: var(--error-color);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid var(--warning-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Recordings list */
.recordings-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.no-recordings {
  text-align: center;
  color: var(--text-muted);
  font-style: italic;
  padding: 2rem;
}

.recording-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  transition: background-color 0.3s ease;
}

.recording-item:hover {
  background: rgba(255, 255, 255, 0.08);
}

.recording-info {
  flex: 1;
  text-align: left;
}

.recording-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.recording-name {
  font-weight: 500;
  color: var(--text-color);
}

.recording-type {
  font-size: 0.8em;
  padding: 0.2em 0.5em;
  background: var(--primary-color);
  color: white;
  border-radius: 4px;
}

.recording-details {
  display: flex;
  gap: 1rem;
  font-size: 0.9em;
  color: var(--text-muted);
}

.recording-controls {
  display: flex;
  gap: 0.5rem;
}

/* Messages */
.error-message, .success-message {
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  font-weight: 500;
}

.error-message {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.success-message {
  background: rgba(74, 222, 128, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(74, 222, 128, 0.3);
}

/* Waveform section */
#waveform-section {
  text-align: center;
  transition: opacity 0.3s ease;
}

#waveform-container {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  border: 1px solid var(--border-color);
  min-height: 100px;
  width: 100%;
  position: relative;
}

#waveform-container:empty::before {
  content: 'Initializing waveform...';
  color: var(--text-muted);
  font-style: italic;
}

/* Utility classes */
.hidden {
  display: none !important;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  #app {
    padding: 1rem;
  }

  header h1 {
    font-size: 2.5em;
  }

  .control-group {
    flex-direction: column;
    align-items: center;
  }

  .recording-item {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .recording-controls {
    justify-content: center;
  }

  .recording-details {
    justify-content: center;
  }
}

@media (prefers-color-scheme: light) {
  :root {
    --background-color: #ffffff;
    --surface-color: #f9f9f9;
    --text-color: #213547;
    --text-muted: #666;
    --border-color: #e5e5e5;
  }
}
