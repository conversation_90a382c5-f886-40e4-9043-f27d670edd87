import { BaseModel } from './BaseModel.js';
import { generateRecordingFilename } from '../utils/uuid.js';

/**
 * Audio Storage Model
 * Handles saving and retrieving audio recordings from localStorage
 */
export class AudioStorageModel extends BaseModel {
  constructor() {
    super();
    this.storageKey = 'aureavoice_recordings';
    this.recordings = this.loadRecordings();
  }

  /**
   * Save audio recording to localStorage
   * @param {Blob} audioBlob - Audio blob to save
   * @param {Object} metadata - Recording metadata
   * @returns {Promise<string>} Recording ID
   */
  async saveRecording(audioBlob, metadata = {}) {
    try {
      const recordingId = generateRecordingFilename();
      
      // Convert blob to base64 for storage
      const base64Data = await this.blobToBase64(audioBlob);
      
      const recording = {
        id: recordingId,
        filename: `${recordingId}.wav`,
        data: base64Data,
        size: audioBlob.size,
        type: audioBlob.type || 'audio/wav',
        timestamp: Date.now(),
        duration: metadata.duration || 0,
        isUpload: metadata.isUpload || false,
        ...metadata
      };

      this.recordings.push(recording);
      this.saveRecordings();
      
      this.notifyObservers('recordingSaved', { recording });
      return recordingId;
    } catch (error) {
      console.error('Error saving recording:', error);
      this.notifyObservers('error', { 
        message: 'Failed to save recording.',
        error 
      });
      throw error;
    }
  }

  /**
   * Get all recordings sorted by timestamp (newest first)
   * @returns {Array} Array of recording objects
   */
  getAllRecordings() {
    return [...this.recordings].sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Get recording by ID
   * @param {string} recordingId - Recording ID
   * @returns {Object|null} Recording object or null if not found
   */
  getRecording(recordingId) {
    return this.recordings.find(recording => recording.id === recordingId) || null;
  }

  /**
   * Get recording as blob
   * @param {string} recordingId - Recording ID
   * @returns {Promise<Blob|null>} Audio blob or null if not found
   */
  async getRecordingBlob(recordingId) {
    const recording = this.getRecording(recordingId);
    if (!recording) {
      return null;
    }

    try {
      return await this.base64ToBlob(recording.data, recording.type);
    } catch (error) {
      console.error('Error converting recording to blob:', error);
      return null;
    }
  }

  /**
   * Delete recording
   * @param {string} recordingId - Recording ID
   * @returns {boolean} Success status
   */
  deleteRecording(recordingId) {
    const index = this.recordings.findIndex(recording => recording.id === recordingId);
    if (index === -1) {
      return false;
    }

    const deletedRecording = this.recordings.splice(index, 1)[0];
    this.saveRecordings();
    
    this.notifyObservers('recordingDeleted', { recording: deletedRecording });
    return true;
  }

  /**
   * Clear all recordings
   */
  clearAllRecordings() {
    const deletedCount = this.recordings.length;
    this.recordings = [];
    this.saveRecordings();
    
    this.notifyObservers('allRecordingsCleared', { deletedCount });
  }

  /**
   * Get storage usage information
   * @returns {Object} Storage usage stats
   */
  getStorageInfo() {
    const totalSize = this.recordings.reduce((sum, recording) => sum + recording.size, 0);
    const recordingCount = this.recordings.length;
    
    // Estimate localStorage usage (base64 encoding increases size by ~33%)
    const estimatedStorageSize = totalSize * 1.33;
    
    return {
      recordingCount,
      totalSize,
      estimatedStorageSize,
      formattedSize: this.formatFileSize(totalSize),
      formattedStorageSize: this.formatFileSize(estimatedStorageSize)
    };
  }

  /**
   * Convert blob to base64 string
   * @param {Blob} blob - Blob to convert
   * @returns {Promise<string>} Base64 string
   */
  blobToBase64(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        // Remove data URL prefix (data:audio/wav;base64,)
        const base64 = reader.result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  /**
   * Convert base64 string to blob
   * @param {string} base64 - Base64 string
   * @param {string} type - MIME type
   * @returns {Promise<Blob>} Blob object
   */
  async base64ToBlob(base64, type = 'audio/wav') {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type });
  }

  /**
   * Load recordings from localStorage
   * @returns {Array} Array of recordings
   */
  loadRecordings() {
    try {
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error loading recordings from localStorage:', error);
      return [];
    }
  }

  /**
   * Save recordings to localStorage
   */
  saveRecordings() {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.recordings));
    } catch (error) {
      console.error('Error saving recordings to localStorage:', error);
      
      // Handle quota exceeded error
      if (error.name === 'QuotaExceededError') {
        this.notifyObservers('storageQuotaExceeded', { 
          message: 'Storage quota exceeded. Please delete some recordings.' 
        });
      }
    }
  }

  /**
   * Format file size for display
   * @param {number} bytes - Size in bytes
   * @returns {string} Formatted size string
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Export recording as downloadable file
   * @param {string} recordingId - Recording ID
   */
  async exportRecording(recordingId) {
    const recording = this.getRecording(recordingId);
    if (!recording) {
      this.notifyObservers('error', { message: 'Recording not found.' });
      return;
    }

    try {
      const blob = await this.getRecordingBlob(recordingId);
      if (!blob) {
        this.notifyObservers('error', { message: 'Failed to load recording data.' });
        return;
      }

      // Create download link
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = recording.filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      this.notifyObservers('recordingExported', { recording });
    } catch (error) {
      console.error('Error exporting recording:', error);
      this.notifyObservers('error', { 
        message: 'Failed to export recording.',
        error 
      });
    }
  }
}
