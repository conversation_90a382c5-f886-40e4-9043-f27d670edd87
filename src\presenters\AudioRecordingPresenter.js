import { BasePresenter } from './BasePresenter.js';

/**
 * Audio Recording Presenter
 * Coordinates between audio models and view
 */
export class AudioRecordingPresenter extends BasePresenter {
  constructor(audioRecordingModel, audioStorageModel, textPromptModel, view) {
    super(null, view); // We have multiple models
    this.audioRecordingModel = audioRecordingModel;
    this.audioStorageModel = audioStorageModel;
    this.textPromptModel = textPromptModel;
    this.currentAudio = null; // For Howler.js audio playback
  }

  /**
   * Initialize the presenter
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    // Note: AudioRecordingModel is only used for processing, not recording
    // No need to initialize microphone access here

    // Setup model observers
    this.setupModelObservers();

    // Setup view event handlers
    this.setupViewEventHandlers();

    // Load initial data
    this.loadInitialData();

    this.isInitialized = true;
  }

  /**
   * Setup model observers
   */
  setupModelObservers() {
    // Audio Recording Model observers
    this.audioRecordingModel.addObserver((event, data) => {
      this.handleAudioRecordingEvent(event, data);
    });

    // Audio Storage Model observers
    this.audioStorageModel.addObserver((event, data) => {
      this.handleAudioStorageEvent(event, data);
    });

    // Text Prompt Model observers
    this.textPromptModel.addObserver((event, data) => {
      this.handleTextPromptEvent(event, data);
    });
  }

  /**
   * Setup view event handlers
   */
  setupViewEventHandlers() {
    this.view.setupEventHandlers({
      onRecordToggle: () => this.handleRecordToggle(),
      onFileSelect: (e) => this.handleFileSelect(e),
      onPromptClick: () => this.handlePromptClick()
    });

    // Custom events from view
    this.view.addEventListener(this.view.element, 'playRecording', (e) => {
      this.handlePlayRecording(e.detail.recordingId);
    });

    this.view.addEventListener(this.view.element, 'downloadRecording', (e) => {
      this.handleDownloadRecording(e.detail.recordingId);
    });

    this.view.addEventListener(this.view.element, 'deleteRecording', (e) => {
      this.handleDeleteRecording(e.detail.recordingId);
    });

    this.view.addEventListener(this.view.element, 'recordingComplete', (e) => {
      this.handleWaveSurferRecordingComplete(e.detail.audioBlob);
    });
  }

  /**
   * Load initial data
   */
  loadInitialData() {
    // Load initial text prompt
    const prompt = this.textPromptModel.getNewPrompt();
    this.view.updateTextPrompt(prompt);

    // Load existing recordings
    const recordings = this.audioStorageModel.getAllRecordings();
    this.view.updateRecordingsList(recordings);
  }

  /**
   * Handle record button toggle (start/stop)
   */
  async handleRecordToggle() {
    const recordPlugin = this.view.getRecordPlugin();

    if (recordPlugin && recordPlugin.isRecording()) {
      // Currently recording, so stop
      recordPlugin.stopRecording();
      // Ensure microphone is properly released
      recordPlugin.stopMic();
      this.view.showRecordingStopped();
    } else {
      // Not recording, so start
      try {
        // Only use WaveSurfer for recording, not AudioRecordingModel
        await this.view.showRecordingStarted();
      } catch (error) {
        console.error('Error starting recording:', error);
        this.view.showError('Failed to start recording. Please check microphone permissions.');
      }
    }
  }

  /**
   * Handle file selection
   */
  async handleFileSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('audio/')) {
      this.view.showError('Please select a valid audio file.');
      this.view.clearFileInput();
      return;
    }

    // Check file size (limit to 50MB)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      this.view.showError('File size too large. Please select a file smaller than 50MB.');
      this.view.clearFileInput();
      return;
    }

    try {
      await this.audioRecordingModel.processUploadedFile(file);
    } catch (error) {
      console.error('Error processing uploaded file:', error);
      this.view.showError('Failed to process uploaded file.');
    }

    this.view.clearFileInput();
  }

  /**
   * Handle text prompt click (get new prompt)
   */
  handlePromptClick() {
    const newPrompt = this.textPromptModel.getNewPrompt();
    this.view.updateTextPrompt(newPrompt);
  }

  /**
   * Handle recording completion from WaveSurfer Record plugin
   */
  async handleWaveSurferRecordingComplete(audioBlob) {
    try {
      this.view.showProcessing();

      // Process the audio blob using the existing AudioRecordingModel
      await this.audioRecordingModel.processUploadedFile(audioBlob);
    } catch (error) {
      console.error('Error processing recorded audio:', error);
      this.view.hideProcessing();
      this.view.showError('Failed to process recording.');
    }
  }

  /**
   * Handle play recording
   */
  async handlePlayRecording(recordingId) {
    try {
      // Stop current audio if playing
      if (this.currentAudio) {
        this.currentAudio.stop();
        this.currentAudio = null;
      }

      const audioBlob = await this.audioStorageModel.getRecordingBlob(recordingId);
      if (!audioBlob) {
        this.view.showError('Failed to load recording.');
        return;
      }

      // Create audio URL and play with Howler.js
      const audioUrl = URL.createObjectURL(audioBlob);
      
      // Import Howler.js dynamically
      const { Howl } = await import('howler');
      
      this.currentAudio = new Howl({
        src: [audioUrl],
        format: ['wav'],
        onplay: () => {
          this.view.updatePlayButton(recordingId, true);
        },
        onpause: () => {
          this.view.updatePlayButton(recordingId, false);
        },
        onend: () => {
          this.view.updatePlayButton(recordingId, false);
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
        },
        onstop: () => {
          this.view.updatePlayButton(recordingId, false);
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
        },
        onloaderror: (_, error) => {
          console.error('Audio load error:', error);
          this.view.showError('Failed to load audio for playback.');
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
        }
      });

      // Toggle play/pause
      if (this.currentAudio.playing()) {
        this.currentAudio.pause();
      } else {
        this.currentAudio.play();
      }

    } catch (error) {
      console.error('Error playing recording:', error);
      this.view.showError('Failed to play recording.');
    }
  }

  /**
   * Handle download recording
   */
  async handleDownloadRecording(recordingId) {
    try {
      await this.audioStorageModel.exportRecording(recordingId);
    } catch (error) {
      console.error('Error downloading recording:', error);
      this.view.showError('Failed to download recording.');
    }
  }

  /**
   * Handle delete recording
   */
  handleDeleteRecording(recordingId) {
    const success = this.audioStorageModel.deleteRecording(recordingId);
    if (!success) {
      this.view.showError('Failed to delete recording.');
    }
  }

  /**
   * Handle audio recording model events
   */
  async handleAudioRecordingEvent(event, data) {
    switch (event) {
      case 'initialized':
        if (!data.success) {
          this.view.showError('Failed to initialize audio recording.');
        }
        break;

      // Note: recordingStarted and recordingStopped are now handled by WaveSurfer Record plugin

      case 'processingStarted':
        this.view.showProcessing();
        break;

      case 'processingCompleted':
        this.view.hideProcessing();
        this.saveProcessedAudio(data);
        break;

      case 'error':
        this.view.hideProcessing();
        this.view.showRecordingStopped();
        this.view.showError(data.message);
        break;
    }
  }

  /**
   * Handle audio storage model events
   */
  handleAudioStorageEvent(event, data) {
    switch (event) {
      case 'recordingSaved':
        this.view.showSuccess(`Recording saved as ${data.recording.filename}`);
        this.updateRecordingsList();
        break;

      case 'recordingDeleted':
        this.view.showSuccess('Recording deleted successfully.');
        this.updateRecordingsList();
        break;

      case 'recordingExported':
        this.view.showSuccess('Recording downloaded successfully.');
        break;

      case 'storageQuotaExceeded':
        this.view.showError(data.message);
        break;

      case 'error':
        this.view.showError(data.message);
        break;
    }
  }

  /**
   * Handle text prompt model events
   */
  handleTextPromptEvent(event, data) {
    switch (event) {
      case 'promptChanged':
        this.view.updateTextPrompt(data.prompt);
        break;

      case 'error':
        this.view.showError(data.message);
        break;
    }
  }

  /**
   * Save processed audio
   */
  async saveProcessedAudio(data) {
    try {
      const metadata = {
        duration: data.duration,
        isUpload: data.isUpload || false
      };

      await this.audioStorageModel.saveRecording(data.audioBlob, metadata);
    } catch (error) {
      console.error('Error saving processed audio:', error);
      this.view.showError('Failed to save recording.');
    }
  }

  /**
   * Update recordings list in view
   */
  updateRecordingsList() {
    const recordings = this.audioStorageModel.getAllRecordings();
    this.view.updateRecordingsList(recordings);
  }

  /**
   * Cleanup presenter
   */
  cleanup() {
    super.cleanup();
    
    // Stop current audio
    if (this.currentAudio) {
      this.currentAudio.stop();
      this.currentAudio = null;
    }

    // Cleanup models
    if (this.audioRecordingModel) {
      this.audioRecordingModel.cleanup();
    }
  }
}
