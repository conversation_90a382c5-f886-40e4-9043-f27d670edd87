import { BaseModel } from './BaseModel.js';

/**
 * Audio Recording Model
 * Handles audio recording with noise suppression and echo cancellation
 */
export class AudioRecordingModel extends BaseModel {
  constructor() {
    super();
    this.mediaRecorder = null;
    this.audioStream = null;
    this.audioChunks = [];
    this.isRecording = false;
    this.audioContext = null;
    this.sourceNode = null;
    this.processorNode = null;
  }

  /**
   * Initialize audio recording with constraints
   */
  async initialize() {
    try {
      // Request microphone access with audio constraints for quality
      const constraints = {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100,
          channelCount: 1
        }
      };

      this.audioStream = await navigator.mediaDevices.getUserMedia(constraints);
      this.notifyObservers('initialized', { success: true });
      return true;
    } catch (error) {
      console.error('Error initializing audio recording:', error);
      this.notifyObservers('error', { 
        message: 'Failed to access microphone. Please check permissions.',
        error 
      });
      return false;
    }
  }

  /**
   * Start recording audio
   */
  async startRecording() {
    if (this.isRecording) {
      return;
    }

    try {
      if (!this.audioStream) {
        const initialized = await this.initialize();
        if (!initialized) {
          return;
        }
      }

      this.audioChunks = [];
      
      // Create MediaRecorder with specific options
      const options = {
        mimeType: 'audio/webm;codecs=opus',
        audioBitsPerSecond: 128000
      };

      this.mediaRecorder = new MediaRecorder(this.audioStream, options);

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = async () => {
        await this.processRecording();
      };

      this.mediaRecorder.start(100); // Collect data every 100ms
      this.isRecording = true;
      
      this.notifyObservers('recordingStarted', { timestamp: Date.now() });
    } catch (error) {
      console.error('Error starting recording:', error);
      this.notifyObservers('error', { 
        message: 'Failed to start recording.',
        error 
      });
    }
  }

  /**
   * Stop recording audio
   */
  stopRecording() {
    if (!this.isRecording || !this.mediaRecorder) {
      return;
    }

    this.mediaRecorder.stop();
    this.isRecording = false;

    // Stop audio stream tracks to release microphone
    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop());
    }

    this.notifyObservers('recordingStopped', { timestamp: Date.now() });
  }

  /**
   * Process recorded audio - convert to WAV and resample to 16kHz
   */
  async processRecording() {
    try {
      this.notifyObservers('processingStarted', {});

      // Create blob from recorded chunks
      const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
      
      // Convert to WAV and resample to 16kHz
      const processedBlob = await this.convertToWav16kHz(audioBlob);
      
      this.notifyObservers('processingCompleted', { 
        audioBlob: processedBlob,
        originalBlob: audioBlob,
        duration: await this.getAudioDuration(processedBlob)
      });
    } catch (error) {
      console.error('Error processing recording:', error);
      this.notifyObservers('error', { 
        message: 'Failed to process recording.',
        error 
      });
    }
  }

  /**
   * Convert audio blob to WAV format at 16kHz sample rate
   * @param {Blob} audioBlob - Original audio blob
   * @returns {Promise<Blob>} Processed WAV blob
   */
  async convertToWav16kHz(audioBlob) {
    return new Promise((resolve, reject) => {
      const fileReader = new FileReader();
      
      fileReader.onload = async (e) => {
        try {
          const arrayBuffer = e.target.result;
          
          // Create audio context for processing
          const audioContext = new (window.AudioContext || window.webkitAudioContext)({
            sampleRate: 16000 // Target sample rate
          });
          
          // Decode audio data
          const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
          
          // Create new buffer at 16kHz
          const targetSampleRate = 16000;
          const targetLength = Math.floor(audioBuffer.length * targetSampleRate / audioBuffer.sampleRate);
          const resampledBuffer = audioContext.createBuffer(1, targetLength, targetSampleRate);
          
          // Resample audio data
          const sourceData = audioBuffer.getChannelData(0);
          const targetData = resampledBuffer.getChannelData(0);
          
          for (let i = 0; i < targetLength; i++) {
            const sourceIndex = Math.floor(i * audioBuffer.length / targetLength);
            targetData[i] = sourceData[sourceIndex];
          }
          
          // Convert to WAV
          const wavBlob = this.audioBufferToWav(resampledBuffer);
          resolve(wavBlob);
          
          audioContext.close();
        } catch (error) {
          reject(error);
        }
      };
      
      fileReader.onerror = reject;
      fileReader.readAsArrayBuffer(audioBlob);
    });
  }

  /**
   * Convert AudioBuffer to WAV Blob
   * @param {AudioBuffer} buffer - Audio buffer to convert
   * @returns {Blob} WAV blob
   */
  audioBufferToWav(buffer) {
    const length = buffer.length;
    const sampleRate = buffer.sampleRate;
    const arrayBuffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(arrayBuffer);
    const channelData = buffer.getChannelData(0);

    // WAV header
    const writeString = (offset, string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * 2, true);

    // Convert float samples to 16-bit PCM
    let offset = 44;
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, channelData[i]));
      view.setInt16(offset, sample * 0x7FFF, true);
      offset += 2;
    }

    return new Blob([arrayBuffer], { type: 'audio/wav' });
  }

  /**
   * Get audio duration from blob
   * @param {Blob} audioBlob - Audio blob
   * @returns {Promise<number>} Duration in seconds
   */
  async getAudioDuration(audioBlob) {
    return new Promise((resolve) => {
      const audio = new Audio();
      audio.onloadedmetadata = () => {
        resolve(audio.duration);
      };
      audio.src = URL.createObjectURL(audioBlob);
    });
  }

  /**
   * Process uploaded audio file
   * @param {File} file - Audio file
   */
  async processUploadedFile(file) {
    try {
      this.notifyObservers('processingStarted', {});
      
      // Convert uploaded file to WAV 16kHz
      const processedBlob = await this.convertToWav16kHz(file);
      
      this.notifyObservers('processingCompleted', { 
        audioBlob: processedBlob,
        originalBlob: file,
        duration: await this.getAudioDuration(processedBlob),
        isUpload: true
      });
    } catch (error) {
      console.error('Error processing uploaded file:', error);
      this.notifyObservers('error', { 
        message: 'Failed to process uploaded file.',
        error 
      });
    }
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop());
      this.audioStream = null;
    }
    
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    this.mediaRecorder = null;
    this.audioChunks = [];
    this.isRecording = false;
  }
}
